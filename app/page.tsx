'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useCalculator } from '@/contexts/CalculatorContext';

import { CircumferenceInput } from '@/components/ui/circumference-input';
import { HeightInput } from '@/components/ui/height-input';

const bodyTypes = [
  { id: 1, fat: '5%' },
  { id: 2, fat: '10%' },
  { id: 3, fat: '15%' },
  { id: 4, fat: '20%' },
  { id: 5, fat: '25%' },
  { id: 6, fat: '30%' },
  { id: 7, fat: '35%' },
];

export default function HealthDataForm() {
  const router = useRouter();
  const { userData, updateUserData, validationErrors, fieldErrors, isDataValid, calculations, forceCalculation, measurementsOptional, setMeasurementsOptional } = useCalculator();

  // Local state for input fields (for display purposes)
  const [heightInput, setHeightInput] = useState('');
  const [weightInput, setWeightInput] = useState('');
  const [waistInput, setWaistInput] = useState('');
  const [hipsInput, setHipsInput] = useState('');
  const [neckInput, setNeckInput] = useState('');
  const [bodyFatInput, setBodyFatInput] = useState('');
  const [selectedBodyType, setSelectedBodyType] = useState(3); // 15% corresponds to type 3

  // Track which fields are being actively edited to prevent formatting interference
  const [activelyEditing, setActivelyEditing] = useState<{ [key: string]: boolean }>({});

  // Update local input fields when userData changes, but only if not actively editing
  useEffect(() => {
    if (!activelyEditing.height) {
      setHeightInput(userData.height > 0 ? userData.height.toString() : '');
    }
    if (!activelyEditing.weight) {
      setWeightInput(userData.weight > 0 ? userData.weight.toString() : '');
    }
    if (!activelyEditing.waist) {
      setWaistInput(userData.measurements?.waist ? userData.measurements.waist.toString() : '');
    }
    if (!activelyEditing.hips) {
      setHipsInput(userData.measurements?.hips ? userData.measurements.hips.toString() : '');
    }
    if (!activelyEditing.neck) {
      setNeckInput(userData.measurements?.neck ? userData.measurements.neck.toString() : '');
    }
    if (!activelyEditing.bodyFat) {
      setBodyFatInput(userData.bodyFat ? userData.bodyFat.toString() : '');
    }
  }, [userData.height, userData.weight, userData.measurements, userData.bodyFat, userData.unitSystem, activelyEditing]);

  // Helper function to constrain values to valid slider ranges
  const constrainToSliderRange = (value: number, min: number, max: number) => {
    return Math.min(Math.max(value, min), max);
  };

  const BodyTypeIcon = ({ type, isSelected, onClick, gender }: { type: number; isSelected: boolean; onClick: () => void; gender: string }) => (
    <div
      className={`relative cursor-pointer transition-all duration-200 ${isSelected ? 'scale-105' : 'hover:scale-102'
        }`}
      onClick={onClick}
    >
      <div className="flex flex-col items-center space-y-1">
        <div
          className="flex items-end justify-center transition-all duration-200"
          style={{ width: '45px', height: '80px' }}
        >
          <img
            src={`/images/${gender === 'male' ? 'muz' : 'zena'}${type - 1}.svg`}
            alt={`Body type ${bodyTypes[type - 1].fat}`}
            className="h-full w-auto object-contain transition-opacity duration-200"
            style={{ opacity: isSelected ? 1 : 0.35 }}
          />
        </div>

      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 ">
      <div className="max-w-2xl mx-auto">
        <Card className="shadow-lg" style={{ backgroundColor: '#F5F5F5' }}>
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-2xl font-bold text-gray-800">
              Enter your data
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Gender Selection */}
            <Tabs value={userData.gender} onValueChange={(value) => {
              updateUserData({ gender: value as 'male' | 'female' });
            }} className="w-full">
              <TabsList className="grid w-full grid-cols-2" style={{ backgroundColor: '#D9D9D9', borderRadius: '12px' }}>
                <TabsTrigger
                  value="female"
                  className="data-[state=active]:text-white data-[state=inactive]:text-gray-600 transition-all duration-200"
                  style={{
                    backgroundColor: userData.gender === 'female' ? '#FFFFFF' : '#D9D9D9',
                    color: userData.gender === 'female' ? '#31860A' : '#515151',
                    borderRadius: '12px 0 0 12px',
                    fontWeight: 'bold',
                    boxShadow: userData.gender === 'female' ? '0 4px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.8)' : 'inset 0 2px 4px rgba(0,0,0,0.1)',
                    transform: userData.gender === 'female' ? 'translateY(-1px)' : 'none',
                    border: userData.gender === 'female' ? '1px solid #e0e0e0' : 'none'
                  }}
                >
                  Female
                </TabsTrigger>
                <TabsTrigger
                  value="male"
                  className="data-[state=active]:text-white data-[state=inactive]:text-gray-600 transition-all duration-200"
                  style={{
                    backgroundColor: userData.gender === 'male' ? '#FFFFFF' : '#D9D9D9',
                    color: userData.gender === 'male' ? '#31860A' : '#515151',
                    borderRadius: '0 12px 12px 0',
                    fontWeight: 'bold',
                    boxShadow: userData.gender === 'male' ? '0 4px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.8)' : 'inset 0 2px 4px rgba(0,0,0,0.1)',
                    transform: userData.gender === 'male' ? 'translateY(-1px)' : 'none',
                    border: userData.gender === 'male' ? '1px solid #e0e0e0' : 'none'
                  }}
                >
                  Male
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Unit System Toggle */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Label className="text-sm font-medium text-gray-700">
                  Unit System
                </Label>
                <div className="flex items-center space-x-2">
                  <span className={`text-sm ${userData.unitSystem === 'metric' ? 'font-bold text-gray-800' : 'text-gray-500'}`}>
                    Metric
                  </span>
                  <Switch
                    checked={userData.unitSystem === 'imperial'}
                    onCheckedChange={(checked) => {
                      updateUserData({ unitSystem: checked ? 'imperial' : 'metric' });
                    }}
                    style={{
                      backgroundColor: userData.unitSystem === 'imperial' ? '#31860A' : '#D1D5DB'
                    }}
                  />
                  <span className={`text-sm ${userData.unitSystem === 'imperial' ? 'font-bold text-gray-800' : 'text-gray-500'}`}>
                    Imperial
                  </span>
                </div>
              </div>
            </div>

            {/* Age */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Label htmlFor="age" className="text-sm font-medium text-gray-700">
                  Age
                </Label>
                <Input
                  id="age"
                  type="number"
                  value={userData.age}
                  onFocus={() => setActivelyEditing(prev => ({ ...prev, age: true }))}
                  onBlur={() => setActivelyEditing(prev => ({ ...prev, age: false }))}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (!isNaN(value)) {
                      updateUserData({ age: value });
                    }
                  }}
                  className={`text-center w-20 h-8 font-bold ${fieldErrors.age ? 'border-red-500' : ''}`}
                  style={{
                    backgroundColor: fieldErrors.age ? 'rgba(239, 68, 68, 0.1)' : '#F5F5F5',
                    border: `solid 1px ${fieldErrors.age ? '#EF4444' : '#CFCFCF'}`,
                    borderRadius: '12px',
                    fontSize: '14px'
                  }}
                  placeholder="25"
                  min="10"
                  max="120"
                />
              </div>

              {/* Age Slider */}
              <div className="relative">
                <div className="w-full h-2 bg-gray-300 rounded-full">
                  {/* Filled track */}
                  <div
                    className="h-2 rounded-full"
                    style={{
                      width: `${Math.min(Math.max(((constrainToSliderRange(userData.age, 10, 120) - 10) / (120 - 10)) * 100, 0), 100)}%`,
                      backgroundColor: '#31860A'
                    }}
                  />
                  <div
                    className="absolute w-6 h-6 bg-white rounded-full shadow-lg cursor-pointer"
                    style={{
                      left: `${Math.min(Math.max(((constrainToSliderRange(userData.age, 10, 120) - 10) / (120 - 10)) * 100, 0), 100)}%`,
                      top: '-8px',
                      transform: 'translateX(-50%)',
                      border: '2px solid #31860A'
                    }}
                  />
                </div>
                <input
                  type="range"
                  min="10"
                  max="120"
                  value={constrainToSliderRange(userData.age, 10, 120)}
                  onChange={(e) => updateUserData({ age: parseInt(e.target.value) })}
                  className="absolute inset-0 w-full h-6 opacity-0 cursor-pointer"
                />
              </div>
            </div>

            {/* Height */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Label htmlFor="height" className="text-sm font-medium text-gray-700">
                  Height
                </Label>
                <HeightInput
                  heightInCm={userData.height}
                  unitSystem={userData.unitSystem}
                  onChange={(height) => updateUserData({ height })}
                  isValid={!fieldErrors.height}
                  onFocus={() => setActivelyEditing(prev => ({ ...prev, height: true }))}
                  onBlur={() => setActivelyEditing(prev => ({ ...prev, height: false }))}
                />
              </div>
              <div className="relative">
                <div className="w-full h-2 bg-gray-300 rounded-full">
                  {/* Filled track */}
                  <div
                    className="h-2 rounded-full"
                    style={{
                      width: `${Math.min(Math.max(((userData.height - 140) / (220 - 140)) * 100, 0), 100)}%`,
                      backgroundColor: '#31860A'
                    }}
                  />
                  <div
                    className="absolute w-6 h-6 bg-white rounded-full shadow-lg cursor-pointer"
                    style={{
                      left: `${Math.min(Math.max(((userData.height - 140) / (220 - 140)) * 100, 0), 100)}%`,
                      top: '-8px',
                      transform: 'translateX(-50%)',
                      border: '2px solid #31860A'
                    }}
                  />
                </div>
                <input
                  type="range"
                  min="140"
                  max="220"
                  value={userData.height}
                  onChange={(e) => updateUserData({ height: parseFloat(e.target.value) })}
                  className="absolute inset-0 w-full h-6 opacity-0 cursor-pointer"
                />
              </div>
            </div>

            {/* Weight */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Label htmlFor="weight" className="text-sm font-medium text-gray-700">
                  Weight
                </Label>
                <div className="relative inline-block">
                  <Input
                    id="weight"
                    type="number"
                    value={weightInput}
                    onFocus={() => setActivelyEditing(prev => ({ ...prev, weight: true }))}
                    onBlur={() => {
                      setActivelyEditing(prev => ({ ...prev, weight: false }));
                    }}
                    onChange={(e) => {
                      setWeightInput(e.target.value);
                      const numericValue = parseFloat(e.target.value);
                      if (!isNaN(numericValue) && numericValue > 0) {
                        updateUserData({ weight: numericValue });
                      }
                    }}
                    className={`w-28 h-8 font-bold ${fieldErrors.weight ? 'border-red-500' : ''}`}
                    style={{
                      backgroundColor: fieldErrors.weight ? 'rgba(239, 68, 68, 0.1)' : '#F5F5F5',
                      border: `solid 1px ${fieldErrors.weight ? '#EF4444' : '#CFCFCF'}`,
                      borderRadius: '12px',
                      fontSize: '14px',
                      textAlign: 'center',
                      paddingRight: '28px',
                      paddingLeft: '8px'
                    }}
                    step="0.1"
                    min="0"
                    placeholder={userData.unitSystem === 'metric' ? '70' : '154'}
                  />
                  <span
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs font-medium pointer-events-none select-none"
                    style={{
                      color: '#9CA3AF',
                      opacity: 0.7,
                      zIndex: 10
                    }}
                  >
                    {userData.unitSystem === 'metric' ? 'kg' : 'lbs'}
                  </span>
                </div>
              </div>
              <div className="relative">
                <div className="w-full h-2 bg-gray-300 rounded-full">
                  {/* Filled track */}
                  <div
                    className="h-2 rounded-full"
                    style={{
                      width: `${Math.min(Math.max(((constrainToSliderRange(userData.weight, 10, 150) - 10) / (150 - 10)) * 100, 0), 100)}%`,
                      backgroundColor: '#31860A'
                    }}
                  />
                  <div
                    className="absolute w-6 h-6 bg-white rounded-full shadow-lg cursor-pointer"
                    style={{
                      left: `${Math.min(Math.max(((constrainToSliderRange(userData.weight, 10, 150) - 10) / (150 - 10)) * 100, 0), 100)}%`,
                      top: '-8px',
                      transform: 'translateX(-50%)',
                      border: '2px solid #31860A'
                    }}
                  />
                </div>
                <input
                  type="range"
                  min="10"
                  max="150"
                  value={constrainToSliderRange(userData.weight, 10, 150)}
                  onChange={(e) => updateUserData({ weight: parseFloat(e.target.value) })}
                  className="absolute inset-0 w-full h-6 opacity-0 cursor-pointer"
                />
              </div>
            </div>

            {/* Activity Level */}
            <div className="space-y-3">
              <Label htmlFor="activity" className="text-sm font-medium text-gray-700">
                Activity
              </Label>
              <Select value={userData.activityLevel} onValueChange={(value) => updateUserData({ activityLevel: value as any })}>
                <SelectTrigger
                  className="h-10 text-sm w-full font-bold"
                  style={{
                    backgroundColor: '#F5F5F5',
                    border: 'solid 1px #CFCFCF',
                    borderRadius: '12px'
                  }}
                >
                  <SelectValue placeholder="Select activity level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sedentary">Sedentary</SelectItem>
                  <SelectItem value="light-activity">Light activity</SelectItem>
                  <SelectItem value="moderate-activity">Moderate activity</SelectItem>
                  <SelectItem value="high-activity">High activity</SelectItem>
                  <SelectItem value="very-high-activity">Very high activity</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Body Fat */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Label htmlFor="body-fat" className="text-sm font-medium text-gray-700">
                  Body fat
                  <span className="text-gray-400 ml-1">ⓘ</span>
                </Label>
                <div className="relative inline-block">
                  <Input
                    id="body-fat"
                    type="number"
                    value={bodyFatInput || (userData.bodyFat ? userData.bodyFat.toString() : '')}
                    onFocus={() => setActivelyEditing(prev => ({ ...prev, bodyFat: true }))}
                    onBlur={() => {
                      setActivelyEditing(prev => ({ ...prev, bodyFat: false }));
                    }}
                    onChange={(e) => {
                      setBodyFatInput(e.target.value);
                      const fatValue = parseFloat(e.target.value);
                      if (!isNaN(fatValue)) {
                        updateUserData({ bodyFat: fatValue });
                        // Update selected body type based on body fat percentage
                        if (fatValue <= 7.5) setSelectedBodyType(1);
                        else if (fatValue <= 12.5) setSelectedBodyType(2);
                        else if (fatValue <= 17.5) setSelectedBodyType(3);
                        else if (fatValue <= 22.5) setSelectedBodyType(4);
                        else if (fatValue <= 27.5) setSelectedBodyType(5);
                        else if (fatValue <= 32.5) setSelectedBodyType(6);
                        else setSelectedBodyType(7);
                      }
                    }}
                    className={`w-28 h-8 font-bold ${fieldErrors.bodyFat ? 'border-red-500' : ''}`}
                    style={{
                      backgroundColor: fieldErrors.bodyFat ? 'rgba(239, 68, 68, 0.1)' : '#F5F5F5',
                      border: `solid 1px ${fieldErrors.bodyFat ? '#EF4444' : '#CFCFCF'}`,
                      borderRadius: '12px',
                      fontSize: '14px',
                      textAlign: 'center',
                      paddingRight: '24px',
                      paddingLeft: '8px'
                    }}
                    step="0.1"
                    min="0"
                    max="50"
                    placeholder="15"
                  />
                  <span
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs font-medium pointer-events-none select-none"
                    style={{
                      color: '#9CA3AF',
                      opacity: 0.7,
                      zIndex: 10
                    }}
                  >
                    %
                  </span>
                </div>
              </div>
              <div className="relative">
                <div className="w-full h-2 bg-gray-300 rounded-full">
                  {/* Filled track */}
                  <div
                    className="h-2 rounded-full"
                    style={{
                      width: `${Math.min(
                        Math.max(((constrainToSliderRange(userData.bodyFat || 15, 5, 45)) - 5) / (45 - 5) * 100, 0),
                        100
                      )}%`,
                      backgroundColor: '#31860A'
                    }}
                  />
                  <div
                    className="absolute w-6 h-6 bg-white rounded-full shadow-lg cursor-pointer"
                    style={{
                      left: `${Math.min(
                        Math.max(((constrainToSliderRange(userData.bodyFat || 15, 5, 45)) - 5) / (45 - 5) * 100, 0),
                        100
                      )}%`,
                      top: '-8px',
                      transform: 'translateX(-50%)',
                      border: '2px solid #31860A'
                    }}
                  />
                </div>
                <input
                  type="range"
                  min="5"
                  max="45"
                  value={constrainToSliderRange(userData.bodyFat || 15, 5, 45)}
                  onChange={(e) => {
                    const fatValue = parseFloat(e.target.value);
                    updateUserData({ bodyFat: fatValue });
                    // Update selected body type based on body fat percentage
                    if (fatValue <= 7.5) setSelectedBodyType(1);
                    else if (fatValue <= 12.5) setSelectedBodyType(2);
                    else if (fatValue <= 17.5) setSelectedBodyType(3);
                    else if (fatValue <= 22.5) setSelectedBodyType(4);
                    else if (fatValue <= 27.5) setSelectedBodyType(5);
                    else if (fatValue <= 32.5) setSelectedBodyType(6);
                    else setSelectedBodyType(7);
                  }}
                  className="absolute inset-0 w-full h-6 opacity-0 cursor-pointer"
                />
              </div>
            </div>

            {/* Body Type Selector */}
            <div className="space-y-4">
              <div className="flex justify-between items-end py-2">
                {bodyTypes.map((type) => (
                  <BodyTypeIcon
                    key={type.id}
                    type={type.id}
                    isSelected={selectedBodyType === type.id}
                    onClick={() => setSelectedBodyType(type.id)}
                    gender={userData.gender}
                  />
                ))}
              </div>
            </div>

            {/* Measures Section */}
            <div className="space-y-4 pt-4 border-t">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Label className="text-sm font-medium text-gray-700">Measures</Label>
                  <Badge variant="outline" className="text-xs">
                    Optional
                  </Badge>
                </div>
                <Switch
                  checked={measurementsOptional}
                  onCheckedChange={setMeasurementsOptional}
                  style={{
                    backgroundColor: measurementsOptional ? '#31860A' : '#D1D5DB'
                  }}
                />
              </div>

              {measurementsOptional && (
                <div className="space-y-4 pt-2">
                  <p className="text-sm text-gray-600">
                    Waist to hip ratio helps determine body type, fat distribution and potential risks.
                  </p>

                  <div className="grid grid-cols-3 gap-4">
                    <div className="flex flex-col space-y-2">
                      <Label className="text-sm font-medium text-gray-700">
                        Neck circumference
                      </Label>
                      <CircumferenceInput
                        value={neckInput}
                        onChange={(value) => {
                          setNeckInput(value);
                          const numericValue = parseFloat(value);
                          if (!isNaN(numericValue) && numericValue > 0) {
                            updateUserData({
                              measurements: {
                                ...userData.measurements,
                                neck: numericValue
                              }
                            });
                          }
                        }}
                        placeholder={userData.unitSystem === 'metric' ? '34' : '13.4'}
                        unitSystem={userData.unitSystem}
                        isValid={!fieldErrors.neck}
                        onFocus={() => setActivelyEditing(prev => ({ ...prev, neck: true }))}
                        onBlur={() => {
                          setActivelyEditing(prev => ({ ...prev, neck: false }));
                        }}
                      />
                    </div>

                    <div className="flex flex-col space-y-2">
                      <Label className="text-sm font-medium text-gray-700">
                        Waist circumference
                      </Label>
                      <CircumferenceInput
                        value={waistInput}
                        onChange={(value) => {
                          setWaistInput(value);
                          const numericValue = parseFloat(value);
                          if (!isNaN(numericValue) && numericValue > 0) {
                            updateUserData({
                              measurements: {
                                ...userData.measurements,
                                waist: numericValue
                              }
                            });
                          }
                        }}
                        placeholder={userData.unitSystem === 'metric' ? '85' : '33.5'}
                        unitSystem={userData.unitSystem}
                        isValid={!fieldErrors.waist}
                        onFocus={() => setActivelyEditing(prev => ({ ...prev, waist: true }))}
                        onBlur={() => {
                          setActivelyEditing(prev => ({ ...prev, waist: false }));
                        }}
                      />
                    </div>

                    <div className="flex flex-col space-y-2">
                      <Label className="text-sm font-medium text-gray-700">
                        Hips circumference
                      </Label>
                      <CircumferenceInput
                        value={hipsInput}
                        onChange={(value) => {
                          setHipsInput(value);
                          const numericValue = parseFloat(value);
                          if (!isNaN(numericValue) && numericValue > 0) {
                            updateUserData({
                              measurements: {
                                ...userData.measurements,
                                hips: numericValue
                              }
                            });
                          }
                        }}
                        placeholder={userData.unitSystem === 'metric' ? '102' : '40.2'}
                        unitSystem={userData.unitSystem}
                        isValid={!fieldErrors.hips}
                        onFocus={() => setActivelyEditing(prev => ({ ...prev, hips: true }))}
                        onBlur={() => {
                          setActivelyEditing(prev => ({ ...prev, hips: false }));
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <h4 className="text-red-800 font-medium text-sm mb-2">Please fix the following errors:</h4>
                <ul className="text-red-700 text-sm space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Submit Button */}
            <div className="pt-6">
              <Button
                className="w-full text-white py-3 rounded-lg font-medium transition-colors duration-200 hover:opacity-95"
                style={{
                  backgroundColor: (isDataValid && calculations) ? '#31850A' : '#9CA3AF',
                  borderRadius: '12px'
                }}
                onClick={() => {
                  if (isDataValid) {
                    // Force calculation to ensure it's ready
                    forceCalculation();
                    // Navigate to results page using Next.js router
                    router.push('/results');
                  }
                }}
                disabled={!isDataValid || !calculations}
              >
                Calculate Results
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}